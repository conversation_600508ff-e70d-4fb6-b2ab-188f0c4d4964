---
type: 'agent_requested'
description: 'Example description'
---

# 🚨 WhatsApp Compliance Rules

## ⚠️ CRITICAL: These are MANDATORY requirements to prevent account bans

> **Violation of these rules can result in permanent WhatsApp account suspension**

## 1️⃣ Account Setup Requirements

### ✅ MUST DO

- **Use WhatsApp Business ONLY** - Never use personal WhatsApp for bots
- **24+ Hour Waiting Period** - Wait minimum 24 hours after registration before scanning QR
- **Manual Activity Period** - Use number manually for 3-5 days before automation
- **Gradual Implementation** - Start with limited functionality, expand gradually

### ❌ NEVER DO

- Use personal WhatsApp accounts for business bots
- Scan QR immediately after registration
- Start with full automation from day one
- Use modified WhatsApp apps (GB WhatsApp, WhatsApp Plus)

## 2️⃣ Message Volume Limits

### 📊 Strict Rate Limits

```yaml
MAXIMUM_LIMITS:
  messages_per_minute: 3-4
  daily_volume: Start low, increase gradually
  concurrent_conversations: Monitor and limit
  broadcast_messages: AVOID completely for first 7 days
```

### 🕐 Timing Requirements

- **Respect Business Hours**: 9 AM - 6 PM local time
- **No 24/7 Operation**: Implement quiet hours
- **Mandatory Delays**: 15+ seconds between messages
- **Batch Processing**: Process messages in small groups with pauses

## 3️⃣ Content Restrictions

### 🚫 PROHIBITED Content

- Spam or unsolicited messages
- Illegal content or malware
- Copyrighted material without permission
- Threatening or harassing content
- Impersonation of other entities
- Adult content or gambling

### ✅ REQUIRED Content Standards

- **Relevant and Valuable**: Every message must provide value
- **Personalized**: Use recipient's name and context
- **Clear Purpose**: State why you're contacting them
- **Easy Opt-out**: Always provide unsubscribe option

## 4️⃣ User Consent Requirements

### 📝 Opt-in Mechanisms

```typescript
// REQUIRED: Explicit consent before messaging
const validateOptIn = (phoneNumber: string): boolean => {
  // Must have explicit user consent
  return database.hasExplicitConsent(phoneNumber);
};

// REQUIRED: Easy opt-out mechanism
const handleOptOut = (phoneNumber: string): void => {
  database.removeConsent(phoneNumber);
  // Stop all future messages immediately
};
```

### 🎯 Contact Initiation

- **User-Initiated Contact**: Use "Contact us on WhatsApp" buttons
- **Invitation Links**: Generate wa.me links with pre-filled messages
- **No Cold Outreach**: Never message users who haven't requested contact
- **Save Contact Incentives**: Encourage users to save your number

## 5️⃣ Technical Implementation Rules

### 🔧 Required Configurations

```typescript
// MANDATORY: Message delay configuration
const MESSAGE_DELAY = 15000; // Minimum 15 seconds

// MANDATORY: Daily limit enforcement
const DAILY_MESSAGE_LIMIT = 100; // Start conservative

// MANDATORY: Queue management
const MAX_QUEUE_SIZE = 50; // Prevent message buildup
```

### 📱 Provider Requirements

- **Use Official APIs**: Meta Business API preferred for production
- **Avoid Unofficial Methods**: No web scraping or unofficial APIs
- **Proper Authentication**: Use official tokens and credentials
- **Monitor API Responses**: Handle rate limit responses properly

## 6️⃣ Monitoring and Compliance

### 📊 Required Monitoring

```typescript
// MANDATORY: Track compliance metrics
interface ComplianceMetrics {
  messagesPerMinute: number;
  dailyMessageCount: number;
  reportedAsSpam: number;
  optOutRequests: number;
  errorRate: number;
}

// MANDATORY: Automated alerts
const checkCompliance = (): void => {
  if (metrics.messagesPerMinute > 4) {
    STOP_ALL_MESSAGING();
    ALERT_TEAM();
  }
};
```

### 🚨 Alert Thresholds

- Messages per minute > 4: **IMMEDIATE STOP**
- Daily messages > limit: **PAUSE UNTIL NEXT DAY**
- Multiple spam reports: **INVESTIGATE IMMEDIATELY**
- High error rate: **REVIEW IMPLEMENTATION**

## 7️⃣ Recovery Protocol (If Banned)

### 🆘 Immediate Actions

1. **STOP ALL MESSAGING** immediately
2. **DO NOT** mention third-party services in appeals
3. **Contact WhatsApp Support** through official channels only
4. **Document** the behavior that caused the ban

### 🔄 New Number Protocol (If Appeal Fails)

```yaml
CRITICAL_REQUIREMENTS:
  - Change physical device (different IMEI)
  - Modify business information
  - Follow new number setup protocol (Section 1)
  - NO broadcast messages for 7 days minimum
  - Reduce to 2 messages/minute for 7 days
  - Clear all message queues
```

## 8️⃣ Compliance Checklist

### ✅ Pre-Launch Checklist

- [ ] WhatsApp Business account properly configured
- [ ] 24+ hour waiting period observed
- [ ] Manual usage period completed (3-5 days)
- [ ] Rate limiting implemented and tested
- [ ] Opt-in/opt-out mechanisms functional
- [ ] Content reviewed for compliance
- [ ] Monitoring and alerting configured
- [ ] Recovery protocol documented

### ✅ Daily Operations Checklist

- [ ] Message volume within limits
- [ ] No spam reports received
- [ ] All opt-out requests processed
- [ ] Error rates acceptable
- [ ] Business hours respected
- [ ] Queue sizes manageable

## 9️⃣ Emergency Procedures

### 🚨 If Account Suspended

1. **IMMEDIATE**: Stop all bot operations
2. **WITHIN 1 HOUR**: Document incident details
3. **WITHIN 24 HOURS**: Submit appeal through official channels
4. **ONGOING**: Monitor appeal status daily

### 📞 Escalation Contacts

- **Technical Issues**: Development Team Lead
- **Compliance Issues**: Compliance Officer
- **Business Impact**: Project Manager

---

## ⚖️ Legal Disclaimer

These rules are based on WhatsApp's Terms of Service and Business Policy as of January 2024. WhatsApp policies may change without notice. It is the responsibility of the development team to stay updated with the latest WhatsApp policies and adjust these rules accordingly.

**🔴 REMEMBER: Account suspension can be permanent. When in doubt, err on the side of caution.**
