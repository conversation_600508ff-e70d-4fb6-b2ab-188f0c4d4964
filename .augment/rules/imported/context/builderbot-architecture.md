---
type: 'agent_requested'
description: 'Example description'
---

# 🏗️ BuilderBot Architecture

## 📖 Overview

BuilderBot is a free, open-source framework for creating chatbots and intelligent applications that connect to different communication channels like WhatsApp, Telegram, and others. Winner of the first prize at OpenExpo 2024 🏆.

## 🎯 Core Philosophy

BuilderBot follows a **modular architecture** with three fundamental components that work together to create a complete chatbot solution:

1. **Flow** - Defines conversation logic and user interactions
2. **Provider** - Handles communication with messaging platforms
3. **Database** - Manages data persistence and user state

This separation allows developers to:

- Switch between different messaging platforms without changing conversation logic
- Use different databases without affecting the bot's behavior
- Scale individual components independently
- Test components in isolation

## 🧩 Core Components

### 1. Flow (Conversation Engine)

The Flow component is responsible for defining how the bot responds to user messages and manages conversation sequences.

```typescript
// Flow defines the conversation logic
const welcomeFlow = addKeyword(['hello', 'hi'])
  .addAnswer('Welcome! How can I help you?')
  .addAnswer('Choose an option:', {
    buttons: [{ body: 'Products' }, { body: 'Support' }],
  });
```

**Key Features:**

- **Keyword Matching**: Trigger responses based on user input
- **Sequential Responses**: Chain multiple messages together
- **Dynamic Content**: Generate responses based on context
- **State Management**: Maintain conversation context
- **Flow Navigation**: Jump between different conversation paths

**Flow Methods:**

- `addKeyword()`: Define trigger words or patterns
- `addAnswer()`: Send messages to users
- `addAction()`: Execute custom logic
- `gotoFlow()`: Navigate to different conversation flows

### 2. Provider (Communication Layer)

The Provider component acts as a bridge between BuilderBot and messaging platforms.

```typescript
// Provider handles platform-specific communication
const adapterProvider = createProvider(BaileysProvider);
// or
const adapterProvider = createProvider(MetaProvider, {
  jwtToken: 'your_token',
  numberId: 'your_number_id',
});
```

**Available Providers:**

| Provider       | Type         | Use Case                 | Setup Complexity |
| -------------- | ------------ | ------------------------ | ---------------- |
| **Meta**       | Official API | Production, Business     | High             |
| **Twilio**     | Official API | Production, Enterprise   | Medium           |
| **Baileys**    | Unofficial   | Development, Testing     | Low              |
| **Venom**      | Unofficial   | Development, Small Scale | Low              |
| **WPPConnect** | Unofficial   | Development, Prototyping | Low              |

**Provider Responsibilities:**

- Message sending and receiving
- Media handling (images, documents, audio)
- Webhook management
- Authentication with messaging platforms
- Rate limiting and error handling

### 3. Database (Persistence Layer)

The Database component provides data persistence and manages user sessions.

```typescript
// Database handles data storage
const adapterDB = new MemoryDB(); // Development
// or
const adapterDB = new MongoAdapter({
  dbUri: 'mongodb://localhost:27017',
  dbName: 'builderbot',
}); // Production
```

**Available Adapters:**

| Database       | Use Case               | Persistence | Scalability |
| -------------- | ---------------------- | ----------- | ----------- |
| **MemoryDB**   | Development, Testing   | No          | Low         |
| **MongoDB**    | Production, NoSQL      | Yes         | High        |
| **MySQL**      | Production, Relational | Yes         | High        |
| **PostgreSQL** | Production, Advanced   | Yes         | High        |

**Database Responsibilities:**

- User session management
- Conversation history storage
- State persistence
- Event logging
- Analytics data collection

## 🔄 Request Flow Architecture

```mermaid
graph TD
    A[User Message] --> B[Provider]
    B --> C[Flow Engine]
    C --> D[Keyword Matching]
    D --> E[Flow Execution]
    E --> F[State Management]
    F --> G[Database]
    E --> H[Response Generation]
    H --> I[Provider]
    I --> J[User Response]

    G --> K[Session Storage]
    G --> L[Event Logging]
```

### Detailed Request Flow

1. **Message Reception**: Provider receives message from messaging platform
2. **Preprocessing**: Message is cleaned and normalized
3. **Keyword Matching**: Flow engine matches message against defined keywords
4. **Flow Selection**: Appropriate flow is selected based on match
5. **Context Loading**: User state and session data loaded from database
6. **Flow Execution**: Selected flow executes its logic
7. **State Updates**: Any state changes are persisted to database
8. **Response Generation**: Flow generates response messages
9. **Message Sending**: Provider sends responses back to user
10. **Logging**: Interaction is logged for analytics and debugging

## 🎛️ Configuration Architecture

### Environment-Based Configuration

```typescript
// config/index.ts
export const config = {
  // Provider Configuration
  provider: {
    type: process.env.PROVIDER_TYPE || 'baileys',
    meta: {
      jwtToken: process.env.META_JWT_TOKEN,
      numberId: process.env.META_NUMBER_ID,
      verifyToken: process.env.META_VERIFY_TOKEN,
    },
    twilio: {
      accountSid: process.env.TWILIO_ACCOUNT_SID,
      authToken: process.env.TWILIO_AUTH_TOKEN,
      vendorNumber: process.env.TWILIO_VENDOR_NUMBER,
    },
  },

  // Database Configuration
  database: {
    type: process.env.DB_TYPE || 'memory',
    mongo: {
      uri: process.env.MONGO_URI,
      dbName: process.env.MONGO_DB_NAME,
    },
    mysql: {
      host: process.env.MYSQL_HOST,
      user: process.env.MYSQL_USER,
      password: process.env.MYSQL_PASSWORD,
      database: process.env.MYSQL_DATABASE,
    },
  },

  // Application Configuration
  app: {
    port: process.env.PORT || 3000,
    environment: process.env.NODE_ENV || 'development',
    logLevel: process.env.LOG_LEVEL || 'info',
  },
};
```

## 🔌 Plugin Architecture

BuilderBot supports a plugin system for extending functionality:

### Available Plugins

- **Telegram**: Extend to Telegram platform
- **Shopify**: E-commerce integration
- **Agents**: AI agent capabilities
- **Langchain**: Advanced AI integration

### Plugin Integration

```typescript
import { TelegramPlugin } from '@builderbot/plugin-telegram';
import { ShopifyPlugin } from '@builderbot/plugin-shopify';

const bot = createBot({
  flow: adapterFlow,
  provider: adapterProvider,
  database: adapterDB,
  plugins: [
    TelegramPlugin,
    ShopifyPlugin.configure({
      apiKey: process.env.SHOPIFY_API_KEY,
      shopDomain: process.env.SHOPIFY_DOMAIN,
    }),
  ],
});
```

## 🚀 Deployment Architecture

### Development Environment

```typescript
// Simple development setup
const adapterDB = new MemoryDB();
const adapterProvider = createProvider(BaileysProvider);
const adapterFlow = createFlow([welcomeFlow]);

const bot = createBot({
  flow: adapterFlow,
  provider: adapterProvider,
  database: adapterDB,
});
```

### Production Environment

```typescript
// Production setup with proper configuration
const adapterDB = new MongoAdapter({
  dbUri: process.env.MONGO_URI,
  dbName: process.env.MONGO_DB_NAME,
});

const adapterProvider = createProvider(MetaProvider, {
  jwtToken: process.env.META_JWT_TOKEN,
  numberId: process.env.META_NUMBER_ID,
  verifyToken: process.env.META_VERIFY_TOKEN,
});

const adapterFlow = createFlow([
  welcomeFlow,
  productsFlow,
  supportFlow,
  checkoutFlow,
]);

const { handleCtx, httpServer } = await createBot({
  flow: adapterFlow,
  provider: adapterProvider,
  database: adapterDB,
});

// Health check endpoint
adapterProvider.server.get('/health', (req, res) => {
  res.json({ status: 'healthy', timestamp: new Date() });
});

httpServer(process.env.PORT || 3000);
```

## 📊 Scalability Considerations

### Horizontal Scaling

```typescript
// Load balancer configuration
const cluster = require('cluster');
const numCPUs = require('os').cpus().length;

if (cluster.isMaster) {
  // Fork workers
  for (let i = 0; i < numCPUs; i++) {
    cluster.fork();
  }

  cluster.on('exit', (worker) => {
    console.log(`Worker ${worker.process.pid} died`);
    cluster.fork();
  });
} else {
  // Worker process
  startBot();
}
```

### Database Scaling

```typescript
// MongoDB with replica set
const adapterDB = new MongoAdapter({
  dbUri:
    'mongodb://mongo1:27017,mongo2:27017,mongo3:27017/builderbot?replicaSet=rs0',
  options: {
    readPreference: 'secondaryPreferred',
    maxPoolSize: 10,
    minPoolSize: 2,
  },
});
```

## 🔍 Monitoring and Observability

### Built-in Monitoring

```typescript
// Event monitoring
bot.on('message_received', (ctx) => {
  metrics.increment('messages.received');
  logger.info('Message received', { from: ctx.from });
});

bot.on('message_sent', (ctx) => {
  metrics.increment('messages.sent');
  logger.info('Message sent', { to: ctx.to });
});

bot.on('error', (error) => {
  metrics.increment('errors.total');
  logger.error('Bot error', error);
});
```

### Performance Metrics

```typescript
// Response time tracking
const responseTimeMiddleware = (ctx, next) => {
  const start = Date.now();

  return next().finally(() => {
    const duration = Date.now() - start;
    metrics.histogram('response_time', duration);
  });
};
```

## 🔧 Advanced Features

### Queue Management

```typescript
// Message queue configuration
const queueConfig = {
  maxSize: 1000,
  processingRate: 10, // messages per second
  retryAttempts: 3,
  retryDelay: 1000,
};
```

### Session Management

```typescript
// Session configuration
const sessionConfig = {
  timeout: 30 * 60 * 1000, // 30 minutes
  cleanupInterval: 5 * 60 * 1000, // 5 minutes
  maxSessions: 10000,
};
```

### Caching Strategy

```typescript
// Multi-level caching
const cacheConfig = {
  memory: {
    maxSize: 1000,
    ttl: 5 * 60 * 1000, // 5 minutes
  },
  redis: {
    host: process.env.REDIS_HOST,
    ttl: 60 * 60 * 1000, // 1 hour
  },
};
```

---

## 📚 Additional Resources

- **Official Documentation**: https://builderbot.vercel.app/
- **GitHub Repository**: https://github.com/codigoencasa/builderbot
- **Community Discord**: https://link.codigoencasa.com/DISCORD
- **Official Course**: https://app.codigoencasa.com/courses/builderbot

This architecture provides a solid foundation for building scalable, maintainable WhatsApp bots while maintaining flexibility for different use cases and deployment scenarios.
