# 📋 BuilderBot Best Practices

## 🎯 Overview

This document outlines recommended best practices for developing efficient, maintainable, and user-friendly WhatsApp bots using BuilderBot framework.

## 1️⃣ Flow Design Best Practices

### 🌊 Conversation Flow Architecture
```typescript
// RECOMMENDED: Modular flow design
const welcomeFlow = addKeyword(['hello', 'hi', 'start'])
    .addAnswer('👋 Welcome! How can I help you today?')
    .addAnswer([
        '1️⃣ View products',
        '2️⃣ Check order status', 
        '3️⃣ Contact support',
        '4️⃣ Account settings'
    ])

const productsFlow = addKeyword(['1', 'products', 'catalog'])
    .addAnswer('🛍️ Here are our product categories:')
    .addAnswer([
        'A) Electronics 📱',
        'B) Clothing 👕', 
        'C) Home & Garden 🏠',
        'D) Books 📚'
    ])

// RECOMMENDED: Clear navigation options
const mainMenuFlow = addKeyword(['menu', 'main', 'back'])
    .addAnswer('🏠 Main Menu:')
    .addAnswer([
        '1️⃣ Products',
        '2️⃣ Orders',
        '3️⃣ Support',
        '0️⃣ Exit'
    ])
```

### 🎨 Message Design Guidelines
```typescript
// RECOMMENDED: Use emojis for visual appeal
.addAnswer('🎉 Order confirmed! Your order #12345 is being processed.')

// RECOMMENDED: Structure information clearly
.addAnswer([
    '📦 Order Details:',
    '• Product: iPhone 15 Pro',
    '• Quantity: 1',
    '• Total: $999.00',
    '• Delivery: 2-3 business days'
])

// RECOMMENDED: Provide clear next steps
.addAnswer([
    '✅ What would you like to do next?',
    '1️⃣ Track shipment',
    '2️⃣ Modify order',
    '3️⃣ Return to menu'
])
```

## 2️⃣ State Management Best Practices

### 💾 Efficient State Usage
```typescript
// RECOMMENDED: Use state for user context
const checkoutFlow = addKeyword(['checkout', 'buy'])
    .addAction(async (ctx, { state, flowDynamic }) => {
        const cart = await getCartItems(ctx.from)
        
        await state.update({
            cartItems: cart,
            checkoutStep: 'address',
            startTime: Date.now()
        })
        
        await flowDynamic(`🛒 Cart total: $${cart.total}`)
    })

// RECOMMENDED: Clear state when appropriate
const exitFlow = addKeyword(['exit', 'quit', 'cancel'])
    .addAction(async (ctx, { state, flowDynamic }) => {
        await state.clear() // Clean up user state
        await flowDynamic('👋 Session ended. Type "hello" to start again.')
    })

// RECOMMENDED: Validate state before use
.addAction(async (ctx, { state, flowDynamic }) => {
    const userState = state.getMyState()
    
    if (!userState?.cartItems?.length) {
        await flowDynamic('🛒 Your cart is empty. Add items first!')
        return
    }
    
    // Proceed with checkout logic
})
```

## 3️⃣ Error Handling and User Experience

### 🛡️ Graceful Error Handling
```typescript
// RECOMMENDED: User-friendly error messages
.addAction(async (ctx, { flowDynamic }) => {
    try {
        const result = await externalAPI.getOrderStatus(ctx.body)
        await flowDynamic(`📦 Order Status: ${result.status}`)
    } catch (error) {
        logger.error('Order lookup failed', { error, userId: ctx.from })
        
        await flowDynamic([
            '❌ Sorry, I couldn\'t find that order.',
            'Please check your order number and try again.',
            'Or type "support" to speak with a human.'
        ])
    }
})

// RECOMMENDED: Fallback options
const fallbackFlow = addKeyword(['help', 'stuck', 'confused'])
    .addAnswer([
        '🤔 Need help? Here are your options:',
        '1️⃣ Return to main menu',
        '2️⃣ Speak with support',
        '3️⃣ View FAQ',
        '4️⃣ Start over'
    ])
```

### ⏱️ Timeout and Idle Handling
```typescript
// RECOMMENDED: Handle user inactivity
const idleFlow = addKeyword(['__idle__'])
    .addAnswer([
        '😴 You\'ve been inactive for a while.',
        'Type "menu" to continue or "exit" to end session.'
    ])

// RECOMMENDED: Session timeout management
.addAction(async (ctx, { state, flowDynamic }) => {
    const userState = state.getMyState()
    const lastActivity = userState?.lastActivity || Date.now()
    const timeoutMinutes = 30
    
    if (Date.now() - lastActivity > timeoutMinutes * 60 * 1000) {
        await state.clear()
        await flowDynamic([
            '⏰ Session expired due to inactivity.',
            'Type "hello" to start a new session.'
        ])
        return
    }
    
    await state.update({ lastActivity: Date.now() })
})
```

## 4️⃣ Performance Optimization

### ⚡ Efficient Data Handling
```typescript
// RECOMMENDED: Cache frequently accessed data
const cache = new Map()

.addAction(async (ctx, { flowDynamic }) => {
    const cacheKey = `products_${ctx.from}`
    
    let products = cache.get(cacheKey)
    if (!products) {
        products = await database.getProducts()
        cache.set(cacheKey, products)
        
        // Set cache expiration
        setTimeout(() => cache.delete(cacheKey), 5 * 60 * 1000) // 5 minutes
    }
    
    await flowDynamic(`Found ${products.length} products`)
})

// RECOMMENDED: Batch database operations
.addAction(async (ctx, { state }) => {
    const userState = state.getMyState()
    
    // Batch multiple updates
    await Promise.all([
        database.updateUserActivity(ctx.from),
        database.logInteraction(ctx.from, 'product_view'),
        analytics.trackEvent('product_viewed', { userId: ctx.from })
    ])
})
```

### 📊 Memory Management
```typescript
// RECOMMENDED: Clean up resources
const cleanupResources = (): void => {
    // Clear expired cache entries
    cache.forEach((value, key) => {
        if (value.expiry < Date.now()) {
            cache.delete(key)
        }
    })
    
    // Clean up old sessions
    sessionManager.cleanExpiredSessions()
}

// Run cleanup every hour
setInterval(cleanupResources, 60 * 60 * 1000)
```

## 5️⃣ User Experience Guidelines

### 🎭 Personality and Tone
```typescript
// RECOMMENDED: Consistent bot personality
const botPersonality = {
    greeting: '👋 Hi there! I\'m your friendly shopping assistant.',
    helpful: '🤝 I\'m here to help! Let me find that for you.',
    apologetic: '😅 Oops! Something went wrong. Let me try again.',
    thankful: '🙏 Thank you for your patience!',
    farewell: '👋 Have a great day! Come back anytime!'
}

// RECOMMENDED: Use consistent tone
.addAnswer(botPersonality.helpful)
.addAnswer('🔍 Searching for your order...')
```

### 📱 Mobile-Friendly Design
```typescript
// RECOMMENDED: Keep messages concise
.addAnswer('📦 Order #12345 shipped!')
.addAnswer('🚚 Tracking: ABC123')
.addAnswer('📅 Delivery: Tomorrow')

// RECOMMENDED: Use numbered options for easy selection
.addAnswer([
    'Choose an option:',
    '1️⃣ Track package',
    '2️⃣ Change address', 
    '3️⃣ Cancel order'
])

// RECOMMENDED: Provide shortcuts
const shortcutFlow = addKeyword(['1', '2', '3', '4', '5'])
    .addAction(async (ctx, { gotoFlow }) => {
        const option = ctx.body
        const flowMap = {
            '1': productsFlow,
            '2': ordersFlow,
            '3': supportFlow,
            '4': accountFlow,
            '5': exitFlow
        }
        
        return gotoFlow(flowMap[option])
    })
```

## 6️⃣ Integration Best Practices

### 🔌 External API Integration
```typescript
// RECOMMENDED: Implement retry logic
const apiCall = async (endpoint: string, data: any, retries = 3): Promise<any> => {
    for (let i = 0; i < retries; i++) {
        try {
            const response = await fetch(endpoint, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data),
                timeout: 10000 // 10 second timeout
            })
            
            if (!response.ok) throw new Error(`HTTP ${response.status}`)
            return await response.json()
            
        } catch (error) {
            if (i === retries - 1) throw error
            await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1))) // Exponential backoff
        }
    }
}

// RECOMMENDED: Handle API failures gracefully
.addAction(async (ctx, { flowDynamic }) => {
    try {
        const result = await apiCall('/api/orders', { userId: ctx.from })
        await flowDynamic(`📦 You have ${result.orders.length} orders`)
    } catch (error) {
        await flowDynamic([
            '⚠️ Service temporarily unavailable.',
            'Please try again in a few minutes.',
            'Type "support" for immediate assistance.'
        ])
    }
})
```

## 7️⃣ Testing and Quality Assurance

### 🧪 Testing Strategies
```typescript
// RECOMMENDED: Test flow logic
describe('Product Flow', () => {
    test('should display product categories', async () => {
        const mockCtx = { from: 'test_user', body: 'products' }
        const mockUtils = { flowDynamic: jest.fn() }
        
        await productsFlow.handler(mockCtx, mockUtils)
        
        expect(mockUtils.flowDynamic).toHaveBeenCalledWith(
            expect.stringContaining('product categories')
        )
    })
})

// RECOMMENDED: Integration testing
const testBot = async (): Promise<void> => {
    const testCases = [
        { input: 'hello', expectedOutput: 'Welcome' },
        { input: 'products', expectedOutput: 'categories' },
        { input: 'invalid', expectedOutput: 'help' }
    ]
    
    for (const testCase of testCases) {
        const result = await simulateUserInput(testCase.input)
        expect(result).toContain(testCase.expectedOutput)
    }
}
```

## 8️⃣ Monitoring and Analytics

### 📊 Performance Monitoring
```typescript
// RECOMMENDED: Track key metrics
const metrics = {
    messagesSent: 0,
    messagesReceived: 0,
    activeUsers: new Set(),
    errorCount: 0,
    responseTime: []
}

.addAction(async (ctx, { flowDynamic }) => {
    const startTime = Date.now()
    
    try {
        // Process user request
        await processUserRequest(ctx)
        
        // Track success metrics
        metrics.messagesSent++
        metrics.activeUsers.add(ctx.from)
        metrics.responseTime.push(Date.now() - startTime)
        
    } catch (error) {
        metrics.errorCount++
        throw error
    }
})

// RECOMMENDED: Regular metrics reporting
setInterval(() => {
    logger.info('Bot metrics', {
        messagesSent: metrics.messagesSent,
        activeUsers: metrics.activeUsers.size,
        avgResponseTime: metrics.responseTime.reduce((a, b) => a + b, 0) / metrics.responseTime.length,
        errorRate: metrics.errorCount / metrics.messagesSent
    })
    
    // Reset counters
    metrics.responseTime = []
}, 60000) // Every minute
```

## 9️⃣ Troubleshooting Guide

### 🔧 Common Issues and Solutions

#### Issue: Bot not responding
```typescript
// SOLUTION: Check provider connection
.addAction(async (ctx, { flowDynamic }) => {
    try {
        await provider.sendMessage(ctx.from, 'Connection test')
    } catch (error) {
        logger.error('Provider connection failed', error)
        // Implement reconnection logic
    }
})
```

#### Issue: Memory leaks
```typescript
// SOLUTION: Implement proper cleanup
const cleanupInterval = setInterval(() => {
    // Clear expired sessions
    sessionManager.cleanup()
    
    // Clear old cache entries
    cache.clear()
    
    // Force garbage collection if available
    if (global.gc) global.gc()
}, 30 * 60 * 1000) // Every 30 minutes
```

---

## ✅ Best Practices Checklist

### Flow Design
- [ ] Clear conversation paths
- [ ] Consistent navigation options
- [ ] Fallback flows for errors
- [ ] User-friendly messages

### Performance
- [ ] Efficient state management
- [ ] Proper error handling
- [ ] Resource cleanup
- [ ] Caching strategies

### User Experience
- [ ] Mobile-friendly design
- [ ] Consistent personality
- [ ] Clear instructions
- [ ] Quick response times

### Quality Assurance
- [ ] Comprehensive testing
- [ ] Performance monitoring
- [ ] Error tracking
- [ ] User feedback collection

**Remember: Great bots are built iteratively. Start simple, test thoroughly, and improve based on user feedback.**
