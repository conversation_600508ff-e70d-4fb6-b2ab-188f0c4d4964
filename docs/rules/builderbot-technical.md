# 🔧 BuilderBot Technical Rules

## ⚠️ MANDATORY: Technical Implementation Requirements

> **These rules ensure proper BuilderBot functionality and prevent technical issues**

## 1️⃣ Core Architecture Requirements

### 🏗️ Required Components
```typescript
// MANDATORY: All three components must be present
import { createBot, createProvider, createFlow, MemoryDB } from '@builderbot/bot'

const main = async () => {
    // REQUIRED: Database adapter
    const adapterDB = new MemoryDB() // or production DB
    
    // REQUIRED: Flow definition
    const adapterFlow = createFlow([/* flows */])
    
    // REQUIRED: Provider configuration
    const adapterProvider = createProvider(/* provider */)
    
    // REQUIRED: Bot creation with all components
    const { handleCtx, httpServer } = await createBot({
        flow: adapterFlow,      // MANDATORY
        provider: adapterProvider, // MANDATORY
        database: adapterDB,    // MANDATORY
    })
}
```

### ❌ FORBIDDEN Patterns
- Creating bots without all three components (Flow, Provider, Database)
- Using deprecated bot-whatsapp imports
- Mixing BuilderBot with other WhatsApp libraries
- Direct provider manipulation without BuilderBot abstraction

## 2️⃣ Flow Definition Rules

### ✅ REQUIRED Flow Structure
```typescript
// MANDATORY: Use addKeyword as entry point
const flow = addKeyword(['trigger'])
    .addAnswer('Response message')
    .addAction(async (ctx, { flowDynamic }) => {
        // Action logic here
    })

// FORBIDDEN: Flows without proper keyword triggers
// FORBIDDEN: Direct message sending without flow context
```

### 🎯 Keyword Requirements
```typescript
// REQUIRED: Proper keyword definition
addKeyword(['hello', 'hi', 'start']) // Multiple triggers OK
addKeyword('hello') // Single trigger OK
addKeyword(REGEX_PATTERN, { regex: true }) // Regex OK with flag

// FORBIDDEN: Empty keyword arrays
// FORBIDDEN: Undefined or null keywords
// FORBIDDEN: Keywords without proper string/regex types
```

### 📝 Answer Requirements
```typescript
// REQUIRED: Proper answer chaining
.addAnswer('Message text')
.addAnswer(['Multi', 'line', 'message']) // Array format OK
.addAnswer('Message with media', { media: 'https://...' })
.addAnswer('Delayed message', { delay: 2000 })

// FORBIDDEN: Empty addAnswer calls
// FORBIDDEN: Non-string/non-array message content
// FORBIDDEN: Invalid media URLs
```

## 3️⃣ Provider Configuration Rules

### 🔌 Production Provider Requirements
```typescript
// REQUIRED: Meta Provider for production
import { MetaProvider } from '@builderbot/provider-meta'

const adapterProvider = createProvider(MetaProvider, {
    jwtToken: process.env.META_JWT_TOKEN,     // REQUIRED
    numberId: process.env.META_NUMBER_ID,     // REQUIRED
    verifyToken: process.env.META_VERIFY_TOKEN, // REQUIRED
    version: 'v16.0',                         // REQUIRED
})

// FORBIDDEN: Hardcoded credentials
// FORBIDDEN: Missing required configuration
// FORBIDDEN: Using development providers in production
```

### 🧪 Development Provider Rules
```typescript
// ALLOWED: For development only
import { BaileysProvider } from '@builderbot/provider-baileys'

const adapterProvider = createProvider(BaileysProvider)

// REQUIRED: Clear environment separation
if (process.env.NODE_ENV === 'production') {
    // Use Meta or Twilio provider
} else {
    // Use Baileys or other QR-based provider
}
```

## 4️⃣ Database Requirements

### 💾 Production Database Rules
```typescript
// REQUIRED: Persistent database for production
import { MongoAdapter } from '@builderbot/database-mongo'

const adapterDB = new MongoAdapter({
    dbUri: process.env.MONGO_URI,    // REQUIRED
    dbName: process.env.MONGO_DB,    // REQUIRED
})

// FORBIDDEN: MemoryDB in production
// FORBIDDEN: Missing database configuration
// FORBIDDEN: Unencrypted database connections
```

### 🧪 Development Database Rules
```typescript
// ALLOWED: MemoryDB for development/testing
import { MemoryDB } from '@builderbot/bot'

const adapterDB = new MemoryDB()

// REQUIRED: Environment-based selection
const adapterDB = process.env.NODE_ENV === 'production' 
    ? new MongoAdapter(config)
    : new MemoryDB()
```

## 5️⃣ Error Handling Requirements

### 🛡️ Mandatory Error Handling
```typescript
// REQUIRED: Try-catch in all async operations
.addAction(async (ctx, { flowDynamic }) => {
    try {
        const result = await externalAPI.call()
        await flowDynamic(`Result: ${result}`)
    } catch (error) {
        console.error('Action error:', error)
        await flowDynamic('Sorry, there was an error. Please try again.')
    }
})

// REQUIRED: Global error handling
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason)
    // Log to monitoring service
})
```

### ❌ FORBIDDEN Error Patterns
- Unhandled promise rejections
- Silent error swallowing
- Exposing internal errors to users
- Missing error logging

## 6️⃣ State Management Rules

### 📊 Required State Handling
```typescript
// REQUIRED: Proper state usage
.addAction({ capture: true }, async (ctx, { state, flowDynamic }) => {
    // REQUIRED: Update state properly
    await state.update({ userInput: ctx.body })
    
    // REQUIRED: Retrieve state safely
    const currentState = state.getMyState()
    
    // REQUIRED: Validate state data
    if (!currentState?.userInput) {
        await flowDynamic('Please provide valid input')
        return
    }
})

// FORBIDDEN: Direct state mutation
// FORBIDDEN: Storing sensitive data in state
// FORBIDDEN: State without validation
```

## 7️⃣ Security Requirements

### 🔒 Mandatory Security Measures
```typescript
// REQUIRED: Environment variables for secrets
const config = {
    jwtToken: process.env.META_JWT_TOKEN,
    apiKey: process.env.EXTERNAL_API_KEY,
    dbPassword: process.env.DB_PASSWORD,
}

// REQUIRED: Input validation
const validatePhoneNumber = (phone: string): boolean => {
    const phoneRegex = /^\+[1-9]\d{1,14}$/
    return phoneRegex.test(phone)
}

// REQUIRED: Sanitize user input
const sanitizeInput = (input: string): string => {
    return input.trim().replace(/[<>]/g, '')
}
```

### ❌ FORBIDDEN Security Practices
- Hardcoded credentials in source code
- Storing passwords in plain text
- Exposing internal system information
- Missing input validation
- Logging sensitive information

## 8️⃣ Performance Requirements

### ⚡ Mandatory Optimizations
```typescript
// REQUIRED: Connection pooling for databases
const adapterDB = new MongoAdapter({
    dbUri: process.env.MONGO_URI,
    maxPoolSize: 10,        // REQUIRED
    minPoolSize: 2,         // REQUIRED
    maxIdleTimeMS: 30000,   // REQUIRED
})

// REQUIRED: Timeout configurations
const httpTimeout = 10000 // 10 seconds max

// REQUIRED: Memory management
const MAX_CONCURRENT_FLOWS = 100
```

## 9️⃣ Logging and Monitoring Rules

### 📊 Required Logging
```typescript
// REQUIRED: Structured logging
import { logger } from './utils/logger'

.addAction(async (ctx, { flowDynamic }) => {
    logger.info('User action', {
        userId: ctx.from,
        action: 'button_click',
        timestamp: new Date().toISOString()
    })
})

// REQUIRED: Error logging
catch (error) {
    logger.error('Flow error', {
        error: error.message,
        stack: error.stack,
        userId: ctx.from,
        flow: 'checkout'
    })
}
```

## 🔟 Deployment Requirements

### 🚀 Production Deployment Rules
```typescript
// REQUIRED: Environment validation
const requiredEnvVars = [
    'META_JWT_TOKEN',
    'META_NUMBER_ID',
    'META_VERIFY_TOKEN',
    'MONGO_URI',
    'NODE_ENV'
]

requiredEnvVars.forEach(envVar => {
    if (!process.env[envVar]) {
        throw new Error(`Missing required environment variable: ${envVar}`)
    }
})

// REQUIRED: Health check endpoint
adapterProvider.server.get('/health', (req, res) => {
    res.json({ 
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: process.env.APP_VERSION
    })
})
```

## ✅ Technical Compliance Checklist

### Pre-Deployment Checklist
- [ ] All three core components properly configured
- [ ] Production database configured (not MemoryDB)
- [ ] Production provider configured (Meta/Twilio)
- [ ] Environment variables properly set
- [ ] Error handling implemented in all flows
- [ ] Input validation implemented
- [ ] Logging and monitoring configured
- [ ] Security measures implemented
- [ ] Performance optimizations applied
- [ ] Health check endpoint functional

---

## 🚨 Critical Reminders

1. **Never use MemoryDB in production** - Data will be lost on restart
2. **Always use environment variables** - Never hardcode credentials
3. **Implement proper error handling** - Prevent bot crashes
4. **Validate all user inputs** - Prevent security vulnerabilities
5. **Monitor performance metrics** - Ensure scalability

**Violation of these technical rules can result in bot failures, security vulnerabilities, and data loss.**
