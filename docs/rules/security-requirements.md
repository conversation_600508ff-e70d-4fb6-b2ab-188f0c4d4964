# 🔒 Security Requirements

## ⚠️ MANDATORY: Security Implementation Rules

> **These security requirements are non-negotiable for production deployment**

## 1️⃣ Environment and Configuration Security

### 🔐 Environment Variables (REQUIRED)
```typescript
// MANDATORY: All sensitive data in environment variables
const config = {
    // WhatsApp/Meta Configuration
    META_JWT_TOKEN: process.env.META_JWT_TOKEN,
    META_NUMBER_ID: process.env.META_NUMBER_ID,
    META_VERIFY_TOKEN: process.env.META_VERIFY_TOKEN,
    
    // Database Configuration
    MONGO_URI: process.env.MONGO_URI,
    DB_PASSWORD: process.env.DB_PASSWORD,
    
    // API Keys
    OPENAI_API_KEY: process.env.OPENAI_API_KEY,
    EXTERNAL_API_KEY: process.env.EXTERNAL_API_KEY,
    
    // Security
    JWT_SECRET: process.env.JWT_SECRET,
    <PERSON>NC<PERSON>YP<PERSON>ON_KEY: process.env.ENCRYPTION_KEY,
}

// REQUIRED: Validate all required environment variables
const validateEnvironment = (): void => {
    const required = Object.keys(config)
    const missing = required.filter(key => !process.env[key])
    
    if (missing.length > 0) {
        throw new Error(`Missing required environment variables: ${missing.join(', ')}`)
    }
}
```

### ❌ FORBIDDEN Practices
- Hardcoding credentials in source code
- Committing .env files to version control
- Storing secrets in configuration files
- Using default or weak passwords
- Exposing environment variables in logs

## 2️⃣ Input Validation and Sanitization

### 🛡️ Required Input Validation
```typescript
// MANDATORY: Validate all user inputs
const validatePhoneNumber = (phone: string): boolean => {
    const phoneRegex = /^\+[1-9]\d{1,14}$/
    return phoneRegex.test(phone?.trim() || '')
}

const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email?.trim() || '')
}

const sanitizeInput = (input: string): string => {
    if (typeof input !== 'string') return ''
    
    return input
        .trim()
        .replace(/[<>]/g, '')           // Remove HTML tags
        .replace(/javascript:/gi, '')    // Remove JS injection
        .replace(/on\w+=/gi, '')        // Remove event handlers
        .substring(0, 1000)             // Limit length
}

// REQUIRED: Use in all user input handling
.addAction({ capture: true }, async (ctx, { flowDynamic, state }) => {
    const userInput = sanitizeInput(ctx.body)
    
    if (!userInput || userInput.length < 2) {
        await flowDynamic('Please provide valid input (minimum 2 characters)')
        return
    }
    
    await state.update({ validatedInput: userInput })
})
```

### 🚫 Input Security Rules
- **NEVER** trust user input without validation
- **ALWAYS** sanitize before processing or storing
- **LIMIT** input length to prevent DoS attacks
- **VALIDATE** format for emails, phones, URLs
- **ESCAPE** special characters in database queries

## 3️⃣ Data Protection and Privacy

### 🔒 Data Encryption Requirements
```typescript
// REQUIRED: Encrypt sensitive data before storage
import crypto from 'crypto'

const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY // 32 bytes key
const ALGORITHM = 'aes-256-gcm'

const encrypt = (text: string): string => {
    const iv = crypto.randomBytes(16)
    const cipher = crypto.createCipher(ALGORITHM, ENCRYPTION_KEY)
    cipher.setAAD(Buffer.from('builderbot', 'utf8'))
    
    let encrypted = cipher.update(text, 'utf8', 'hex')
    encrypted += cipher.final('hex')
    
    const authTag = cipher.getAuthTag()
    
    return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted
}

const decrypt = (encryptedData: string): string => {
    const parts = encryptedData.split(':')
    const iv = Buffer.from(parts[0], 'hex')
    const authTag = Buffer.from(parts[1], 'hex')
    const encrypted = parts[2]
    
    const decipher = crypto.createDecipher(ALGORITHM, ENCRYPTION_KEY)
    decipher.setAAD(Buffer.from('builderbot', 'utf8'))
    decipher.setAuthTag(authTag)
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8')
    decrypted += decipher.final('utf8')
    
    return decrypted
}
```

### 📊 Data Handling Rules
```typescript
// REQUIRED: Classify and handle data appropriately
interface UserData {
    // Public data - can be logged/cached
    userId: string
    preferences: object
    
    // Sensitive data - encrypt before storage
    phoneNumber: string    // ENCRYPT
    email: string         // ENCRYPT
    personalInfo: object  // ENCRYPT
    
    // Restricted data - never store
    // passwords, payment info, etc.
}

// REQUIRED: Data retention policy
const DATA_RETENTION_DAYS = 90

const cleanupOldData = async (): Promise<void> => {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - DATA_RETENTION_DAYS)
    
    await database.deleteMany({
        createdAt: { $lt: cutoffDate },
        dataType: 'conversation_history'
    })
}
```

## 4️⃣ Authentication and Authorization

### 🔑 API Security Requirements
```typescript
// REQUIRED: Secure API endpoints
import jwt from 'jsonwebtoken'

const authenticateRequest = (req: Request, res: Response, next: NextFunction): void => {
    const token = req.headers.authorization?.replace('Bearer ', '')
    
    if (!token) {
        return res.status(401).json({ error: 'Authentication required' })
    }
    
    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET!)
        req.user = decoded
        next()
    } catch (error) {
        return res.status(401).json({ error: 'Invalid token' })
    }
}

// REQUIRED: Rate limiting
const rateLimit = require('express-rate-limit')

const apiLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP'
})

// REQUIRED: Apply to all API routes
adapterProvider.server.use('/api/', apiLimiter)
adapterProvider.server.use('/api/', authenticateRequest)
```

## 5️⃣ Logging and Monitoring Security

### 📊 Secure Logging Requirements
```typescript
// REQUIRED: Secure logging configuration
const logger = {
    info: (message: string, data?: object) => {
        const logData = sanitizeLogData(data)
        console.log(JSON.stringify({
            level: 'info',
            message,
            data: logData,
            timestamp: new Date().toISOString()
        }))
    },
    
    error: (message: string, error?: Error, data?: object) => {
        const logData = sanitizeLogData(data)
        console.error(JSON.stringify({
            level: 'error',
            message,
            error: error?.message,
            stack: error?.stack,
            data: logData,
            timestamp: new Date().toISOString()
        }))
    }
}

// REQUIRED: Sanitize sensitive data from logs
const sanitizeLogData = (data?: object): object => {
    if (!data) return {}
    
    const sanitized = { ...data }
    
    // Remove sensitive fields
    const sensitiveFields = ['password', 'token', 'apiKey', 'phoneNumber', 'email']
    sensitiveFields.forEach(field => {
        if (sanitized[field]) {
            sanitized[field] = '[REDACTED]'
        }
    })
    
    return sanitized
}
```

### ❌ FORBIDDEN Logging Practices
- Logging passwords or tokens
- Logging personal information (PII)
- Logging credit card or payment information
- Exposing internal system paths
- Logging without proper sanitization

## 6️⃣ Network and Communication Security

### 🌐 HTTPS and TLS Requirements
```typescript
// REQUIRED: Force HTTPS in production
const enforceHTTPS = (req: Request, res: Response, next: NextFunction): void => {
    if (process.env.NODE_ENV === 'production' && !req.secure) {
        return res.redirect(301, `https://${req.headers.host}${req.url}`)
    }
    next()
}

// REQUIRED: Security headers
const helmet = require('helmet')

adapterProvider.server.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
        },
    },
    hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
    }
}))
```

## 7️⃣ Incident Response Requirements

### 🚨 Security Incident Protocol
```typescript
// REQUIRED: Security incident detection
const detectSecurityIncident = (event: SecurityEvent): void => {
    const incidents = [
        'multiple_failed_auth',
        'suspicious_input_pattern',
        'rate_limit_exceeded',
        'unauthorized_access_attempt'
    ]
    
    if (incidents.includes(event.type)) {
        triggerSecurityAlert(event)
    }
}

// REQUIRED: Automated response
const triggerSecurityAlert = (event: SecurityEvent): void => {
    // Log incident
    logger.error('Security incident detected', event)
    
    // Notify security team
    notifySecurityTeam(event)
    
    // Implement automatic countermeasures
    if (event.severity === 'high') {
        temporaryBlockIP(event.sourceIP)
    }
}
```

### 📋 Incident Response Checklist
1. **Immediate Response** (0-1 hour):
   - [ ] Identify and contain the incident
   - [ ] Assess the scope and impact
   - [ ] Notify the security team
   - [ ] Document initial findings

2. **Investigation** (1-24 hours):
   - [ ] Analyze logs and evidence
   - [ ] Determine root cause
   - [ ] Assess data exposure
   - [ ] Document timeline

3. **Recovery** (24-72 hours):
   - [ ] Implement fixes
   - [ ] Restore normal operations
   - [ ] Monitor for recurring issues
   - [ ] Update security measures

## 8️⃣ Security Compliance Checklist

### ✅ Pre-Production Security Audit
- [ ] All environment variables properly configured
- [ ] Input validation implemented for all user inputs
- [ ] Sensitive data encryption implemented
- [ ] Authentication and authorization configured
- [ ] Rate limiting implemented
- [ ] Secure logging configured
- [ ] HTTPS enforced
- [ ] Security headers configured
- [ ] Incident response procedures documented
- [ ] Security monitoring implemented

### ✅ Ongoing Security Maintenance
- [ ] Regular security updates applied
- [ ] Environment variables rotated quarterly
- [ ] Access logs reviewed weekly
- [ ] Security incidents documented and analyzed
- [ ] Penetration testing performed annually

---

## 🚨 Security Emergency Contacts

- **Security Incident**: <EMAIL>
- **Data Breach**: <EMAIL>
- **Technical Emergency**: <EMAIL>

**Remember: Security is everyone's responsibility. When in doubt, choose the more secure option.**
